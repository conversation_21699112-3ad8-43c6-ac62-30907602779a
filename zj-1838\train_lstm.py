import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
import pickle
import os
from scipy.signal import butter, filtfilt


# Butterworth lowpass filter
def butter_lowpass_filter(data, cutoff=0.001, fs=1.0, order=4):
    """
    Apply Butterworth lowpass filter
    :param data: Input data
    :param cutoff: Cutoff frequency (Hz)
    :param fs: Sampling frequency (Hz)
    :param order: Filter order
    :return: Filtered data
    """
    # Calculate normalized cutoff frequency
    nyq = 0.5 * fs  # Nyquist frequency
    normal_cutoff = cutoff / nyq

    # Design Butterworth filter
    b, a = butter(order, normal_cutoff, btype='low', analog=False)

    # Apply zero-phase filtering
    y = filtfilt(b, a, data.flatten())
    return y.reshape(-1, 1)


# 1. Load and filter data
def load_data(file_path, column_name='zd', filter_data=True):
    try:
        # Try reading Excel file
        data = pd.read_excel(file_path, engine='openpyxl')[[column_name]].values
    except:
        try:
            # Try reading CSV file
            data = pd.read_csv(file_path, encoding='gbk')[[column_name]].values
        except Exception as e:
            print(f"Cannot read file: {e}")
            exit()

    # Plot original data
    plt.figure(figsize=(12, 6))
    plt.plot(data, 'b-', label='Original')
    plt.title('Original Data')
    plt.legend()
    plt.show()

    # Apply lowpass filter
    if filter_data:
        print("Applying Butterworth lowpass filter (cutoff=0.001, order=4)...")
        filtered_data = butter_lowpass_filter(data, cutoff=0.001, fs=1.0, order=4)

        # Plot comparison before and after filtering
        plt.figure(figsize=(12, 6))
        plt.plot(data, 'b-', alpha=0.5, label='Original')
        plt.plot(filtered_data, 'r-', linewidth=1.5, label='Filtered')
        plt.title('Filtered vs Original Data')
        plt.legend()
        plt.show()

        return filtered_data

    return data


# 2. Create autoregressive dataset (multi-step prediction)
def create_autoregressive_dataset(data, look_back=200, pred_steps=50):
    """Create autoregressive training dataset"""
    X, y = [], []
    for i in range(len(data) - look_back - pred_steps):
        # Initial input sequence
        seq = data[i:i + look_back, 0]
        targets = []

        # Multi-step prediction targets
        for j in range(pred_steps):
            targets.append(data[i + look_back + j, 0])

        X.append(seq)
        y.append(targets)
    return np.array(X), np.array(y)


# 3. Main training function
def train_model(data_file, look_back=3000, epochs=15, batch_size=30, pred_steps=50, filter_data=True):
    # Load and filter data
    data = load_data(data_file, filter_data=filter_data)
    print(f"Data loaded successfully, total records: {len(data)}")
    print(f"Using parameters: look_back={look_back}, pred_steps={pred_steps}, epochs={epochs}")

    # Data normalization
    scaler = MinMaxScaler(feature_range=(0, 1))
    data_normalized = scaler.fit_transform(data)

    # Split into train and test sets
    train_size = int(len(data_normalized) * 0.8)
    train, test = data_normalized[0:train_size], data_normalized[train_size:]
    print(f"Training set size: {len(train)}, Test set size: {len(test)}")

    # Create autoregressive time window dataset
    X_train, y_train = create_autoregressive_dataset(train, look_back, pred_steps)
    X_test, y_test = create_autoregressive_dataset(test, look_back, pred_steps)
    print(f"Training samples: {X_train.shape[0]}, Test samples: {X_test.shape[0]}")

    # Reshape input dimensions [samples, timesteps, features]
    X_train = np.reshape(X_train, (X_train.shape[0], look_back, 1))
    X_test = np.reshape(X_test, (X_test.shape[0], look_back, 1))

    # Build LSTM model (multi-step prediction)
    model = Sequential()
    model.add(LSTM(64, return_sequences=True, input_shape=(look_back, 1)))
    model.add(LSTM(32))
    model.add(Dense(pred_steps))

    model.compile(optimizer='adam', loss='mean_squared_error')
    model.summary()

    # Train model
    print("Starting model training...")
    history = model.fit(
        X_train,
        y_train,
        epochs=epochs,
        batch_size=batch_size,
        validation_data=(X_test, y_test),
        verbose=1
    )

    # Plot training history
    plt.figure(figsize=(10, 5))
    plt.plot(history.history['loss'], label='Train Loss')
    plt.plot(history.history['val_loss'], label='Test Loss')
    plt.title('Training Loss Curve')
    plt.ylabel('MSE Loss')
    plt.xlabel('Epochs')
    plt.legend()
    plt.show()

    # Evaluate model
    train_predict = model.predict(X_train)
    test_predict = model.predict(X_test)

    # Inverse normalization - evaluate only the last step prediction
    # Training set evaluation
    train_last_step = train_predict[:, -1].reshape(-1, 1)
    train_last_actual = y_train[:, -1].reshape(-1, 1)

    train_predict_inv = scaler.inverse_transform(train_last_step)
    y_train_inv = scaler.inverse_transform(train_last_actual)

    # Test set evaluation
    test_last_step = test_predict[:, -1].reshape(-1, 1)
    test_last_actual = y_test[:, -1].reshape(-1, 1)

    test_predict_inv = scaler.inverse_transform(test_last_step)
    y_test_inv = scaler.inverse_transform(test_last_actual)

    # Calculate RMSE
    train_score = np.sqrt(mean_squared_error(y_train_inv, train_predict_inv))
    test_score = np.sqrt(mean_squared_error(y_test_inv, test_predict_inv))
    print(f'Train RMSE (last step): {train_score:.4f}')
    print(f'Test RMSE (last step): {test_score:.4f}')

    # Save model and key parameters
    model.save('lstm_model.h5')

    # Save scaler and configuration
    with open('lstm_config.pkl', 'wb') as f:
        pickle.dump({
            'scaler': scaler,
            'look_back': look_back,
            'train_size': train_size,
            'pred_steps': pred_steps,
            'filtered': filter_data
        }, f)

    print("Model and configuration saved: lstm_model.h5 and lstm_config.pkl")

    # Visualize full prediction results
    plt.figure(figsize=(15, 8))

    # Original data
    plt.plot(data, 'b-', label='Original', alpha=0.6)

    # Training set prediction (only last step)
    train_predict_plot = np.empty_like(data)
    train_predict_plot[:, :] = np.nan
    last_step_train = train_predict[:, -1].reshape(-1, 1)
    train_predict_plot[look_back + pred_steps - 1: look_back + pred_steps - 1 + len(last_step_train)] = last_step_train

    # Inverse normalization for training prediction
    train_predict_inv_plot = scaler.inverse_transform(train_predict_plot)
    plt.plot(train_predict_inv_plot, 'r-', linewidth=1.5, label='Train Prediction')

    # Test set prediction (only last step)
    test_predict_plot = np.empty_like(data)
    test_predict_plot[:, :] = np.nan
    last_step_test = test_predict[:, -1].reshape(-1, 1)
    test_start = train_size + look_back + pred_steps - 1
    test_predict_plot[test_start: test_start + len(last_step_test)] = last_step_test

    # Inverse normalization for test prediction
    test_predict_inv_plot = scaler.inverse_transform(test_predict_plot)
    plt.plot(test_predict_inv_plot, 'g-', linewidth=1.5, label='Test Prediction')

    plt.title('LSTM Predictions')
    plt.xlabel('Timestep')
    plt.ylabel('Value')
    plt.legend()
    plt.show()

    return model, scaler, look_back, pred_steps, data, data_normalized


# 4. Prediction function
def predict_future(model, scaler, look_back, pred_steps, data_normalized, total_steps=3000):
    """
    Predict future data using trained LSTM model
    :param model: Trained LSTM model
    :param scaler: Data scaler
    :param look_back: Input sequence length
    :param pred_steps: Steps to predict each iteration
    :param data_normalized: Normalized historical data
    :param total_steps: Total steps to predict
    :return: Denormalized predictions
    """
    # Use last part of historical data as initial input
    current_input = data_normalized[-look_back:].reshape(1, look_back, 1)

    # Store all predictions (normalized)
    predictions_normalized = []

    # Calculate number of predictions needed
    n_predictions = int(np.ceil(total_steps / pred_steps))

    print(f"Starting prediction of {total_steps} future steps...")
    for i in range(n_predictions):
        # Predict next segment
        pred = model.predict(current_input, verbose=0)

        # Store predictions
        predictions_normalized.extend(pred[0])

        # Update input sequence: remove oldest, add newest predictions
        current_input = np.append(
            current_input[:, pred_steps:, :],
            pred.reshape(1, pred_steps, 1),
            axis=1
        )

    # Keep only requested prediction steps
    predictions_normalized = np.array(predictions_normalized)[:total_steps]

    # Denormalize predictions
    predictions = scaler.inverse_transform(predictions_normalized.reshape(-1, 1))

    return predictions


# 5. Plot combined results
def plot_combined_results(original_data, predictions, look_back):
    """
    Visualize original data and predictions
    :param original_data: Original data (not normalized)
    :param predictions: Predictions
    :param look_back: Input sequence length
    """
    plt.figure(figsize=(15, 8))

    # Plot original data
    plt.plot(original_data, 'b-', label='Original Data', alpha=0.7)

    # Plot predictions
    prediction_timesteps = np.arange(len(original_data), len(original_data) + len(predictions))
    plt.plot(prediction_timesteps, predictions, 'r-', linewidth=1.5, label='Predictions')

    # Mark prediction start point
    plt.axvline(x=len(original_data), color='g', linestyle='--', label='Prediction Start')

    plt.title(f'LSTM Prediction Results ({len(predictions)} steps)')
    plt.xlabel('Timestep')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()


# 6. Main function to handle both training and prediction
def train_and_predict(data_file, total_steps=3000, look_back=3000, epochs=5, batch_size=320, pred_steps=200,
                      apply_filter=True):
    """
    Main function to train model and make predictions
    :param data_file: Data file path
    :param total_steps: Total steps to predict
    :param look_back: Input sequence length
    :param epochs: Training epochs
    :param batch_size: Training batch size
    :param pred_steps: Steps to predict each iteration
    :param apply_filter: Whether to apply lowpass filter
    """
    # Train the model
    print("=" * 50)
    print("Starting model training...")
    print("=" * 50)
    model, scaler, look_back, pred_steps, original_data, data_normalized = train_model(
        data_file,
        look_back=look_back,
        epochs=epochs,
        batch_size=batch_size,
        pred_steps=pred_steps,
        filter_data=apply_filter
    )

    print("\n" + "=" * 50)
    print("Training completed. Starting prediction...")
    print("=" * 50)

    # Make predictions
    predictions = predict_future(
        model=model,
        scaler=scaler,
        look_back=look_back,
        pred_steps=pred_steps,
        data_normalized=data_normalized,
        total_steps=total_steps
    )

    # Save predictions
    output_df = pd.DataFrame({
        'timestep': range(len(original_data), len(original_data) + len(predictions)),
        'predicted_value': predictions.flatten()
    })

    output_file = 'predictions.xlsx'
    output_df.to_excel(output_file, index=False)
    print(f"Predictions saved to: {output_file}")

    # Visualize combined results
    plot_combined_results(original_data, predictions, look_back)


if __name__ == "__main__":
    # Configuration parameters
    DATA_FILE = 'input.xlsx'  # Or 'input.csv'
    PREDICTION_STEPS = 3000  # Steps to predict
    LOOK_BACK = 300  # Input sequence length
    EPOCHS = 15  # Training epochs
    BATCH_SIZE = 50  # Batch size
    PRED_STEPS = 20  # Steps to predict each iteration
    APPLY_FILTER = True  # Apply lowpass filter

    # Run training and prediction
    train_and_predict(
        data_file=DATA_FILE,
        total_steps=PREDICTION_STEPS,
        look_back=LOOK_BACK,
        epochs=EPOCHS,
        batch_size=BATCH_SIZE,
        pred_steps=PRED_STEPS,
        apply_filter=APPLY_FILTER
    )