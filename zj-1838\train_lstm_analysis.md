# LSTM时间序列预测模型分析文档

## 概述

`train_lstm.py` 是一个基于TensorFlow/Keras的LSTM（长短期记忆）神经网络时间序列预测系统。该系统实现了完整的数据预处理、模型训练、预测和可视化流程，专门用于时间序列数据的多步预测。

## 主要功能

### 1. 数据预处理
- **数据加载**: 支持Excel (.xlsx) 和CSV文件格式
- **数据过滤**: 使用Butterworth低通滤波器去除噪声
- **数据标准化**: 使用MinMaxScaler将数据归一化到[0,1]范围
- **数据可视化**: 显示原始数据和过滤后数据的对比图

### 2. 模型架构
- **网络结构**: 双层LSTM + 全连接层
  - 第一层LSTM: 64个神经元，返回序列
  - 第二层LSTM: 32个神经元
  - 输出层: 全连接层，输出多步预测结果
- **优化器**: Adam优化器
- **损失函数**: 均方误差(MSE)

### 3. 训练策略
- **自回归数据集**: 使用滑动窗口创建训练样本
- **多步预测**: 一次预测多个时间步长
- **数据分割**: 80%训练集，20%测试集
- **模型保存**: 自动保存训练好的模型和配置参数

## 核心函数分析

### 1. `butter_lowpass_filter(data, cutoff=0.001, fs=1.0, order=4)`
**功能**: 应用Butterworth低通滤波器
- **参数**:
  - `data`: 输入数据
  - `cutoff`: 截止频率 (默认0.001 Hz)
  - `fs`: 采样频率 (默认1.0 Hz)
  - `order`: 滤波器阶数 (默认4阶)
- **返回**: 过滤后的数据

### 2. `load_data(file_path, column_name='zd', filter_data=True)`
**功能**: 加载和预处理数据
- **支持格式**: Excel和CSV文件
- **编码处理**: CSV文件使用GBK编码
- **可选过滤**: 可选择是否应用低通滤波
- **可视化**: 自动显示原始数据和过滤效果

### 3. `create_autoregressive_dataset(data, look_back=200, pred_steps=50)`
**功能**: 创建自回归训练数据集
- **滑动窗口**: 使用固定长度的历史数据作为输入
- **多步输出**: 每个样本对应多个连续的预测目标
- **数据结构**: 返回(X, y)格式的训练数据

### 4. `train_model(data_file, look_back=3000, epochs=15, batch_size=30, pred_steps=50, filter_data=True)`
**功能**: 主要的模型训练函数
- **完整流程**: 数据加载→预处理→模型构建→训练→评估→保存
- **参数配置**:
  - `look_back`: 输入序列长度 (默认3000)
  - `epochs`: 训练轮数 (默认15)
  - `batch_size`: 批次大小 (默认30)
  - `pred_steps`: 预测步数 (默认50)
- **输出**: 训练损失曲线和预测结果可视化

### 5. `predict_future(model, scaler, look_back, pred_steps, data_normalized, total_steps=3000)`
**功能**: 使用训练好的模型进行未来预测
- **迭代预测**: 使用预测结果更新输入序列
- **长期预测**: 支持预测任意长度的未来序列
- **自动反归一化**: 将预测结果转换回原始数据范围

### 6. `train_and_predict(data_file, total_steps=3000, ...)`
**功能**: 集成训练和预测的主函数
- **一站式解决方案**: 完成从数据加载到结果保存的全流程
- **结果保存**: 自动保存预测结果到Excel文件
- **可视化**: 显示原始数据和预测结果的组合图

## 技术特点

### 优势
1. **完整的工作流程**: 从数据预处理到结果可视化的完整pipeline
2. **灵活的参数配置**: 支持多种超参数调整
3. **多格式支持**: 兼容Excel和CSV数据格式
4. **噪声处理**: 内置Butterworth滤波器去除数据噪声
5. **多步预测**: 支持一次预测多个时间步长
6. **可视化丰富**: 提供多种图表展示训练和预测效果

### 技术亮点
1. **自回归架构**: 使用历史数据预测未来，适合时间序列特性
2. **零相位滤波**: 使用`filtfilt`避免相位失真
3. **批量预测**: 通过迭代方式实现长期预测
4. **模型持久化**: 保存模型和配置便于后续使用

## 使用方法

### 基本配置
```python
DATA_FILE = 'input.xlsx'        # 数据文件
PREDICTION_STEPS = 3000         # 预测步数
LOOK_BACK = 300                 # 输入序列长度
EPOCHS = 15                     # 训练轮数
BATCH_SIZE = 50                 # 批次大小
PRED_STEPS = 20                 # 每次预测步数
APPLY_FILTER = True             # 是否应用滤波
```

### 运行方式
```bash
python train_lstm.py
```

## 输出文件

1. **lstm_model.h5**: 训练好的LSTM模型
2. **lstm_config.pkl**: 模型配置和数据预处理参数
3. **predictions.xlsx**: 预测结果Excel文件

## 依赖库

- `numpy`: 数值计算
- `pandas`: 数据处理
- `matplotlib`: 数据可视化
- `tensorflow.keras`: 深度学习框架
- `sklearn`: 机器学习工具
- `scipy`: 科学计算（信号处理）
- `pickle`: 对象序列化

## 适用场景

该系统特别适用于：
- 连续时间序列数据预测
- 需要去噪处理的信号预测
- 中长期趋势预测
- 需要多步预测的应用场景

## 注意事项

1. **数据质量**: 确保输入数据的连续性和一致性
2. **参数调优**: 根据具体数据特征调整网络参数
3. **计算资源**: 大的`look_back`值会增加内存和计算需求
4. **过拟合风险**: 监控训练和验证损失，避免过拟合
