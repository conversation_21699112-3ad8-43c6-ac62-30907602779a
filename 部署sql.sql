-- 菜单数据插入
INSERT INTO bc_pmctl_menu (id,system_code,menu_name,menu_type,menu_url,route_url,component_url,parent_id,display_order,icon,status,create_user,create_time,update_user,update_time) VALUES
	('e6af76480a6246069ebf10060516cc0d','yoaf','对账/清算不一致查询','2','','recSettMismatch/index','xzp/recSettMismatch/index','c1486f2e3551431dba1dc1e94068fd7d',9,'clipboard','1','admin','2025-07-10 09:39:19.926','admin','2025-07-10 09:39:19.926'),
	('d2dc8dea6c034d8eb79a8076dff06854', 'yoaf', '监管账户资金变动情况反馈', '2', '', 'acctquery/index', 'xzp/acctquery/index', 'c1486f2e3551431dba1dc1e94068fd7d', 10, 'message', '1', 'admin', '2025-07-11 14:50:39.589', 'admin', '2025-07-11 14:51:16.29'),
	('7e7f45dae6ef463686fe95be2225f850', 'yoaf', '监管账户管理', '2', '', 'acctmgmt/index', 'xzp/acctmgmt/index', 'c1486f2e3551431dba1dc1e94068fd7d', 8, 'department', '1', 'admin', '2025-07-15 16:00:52.519', 'admin', '2025-07-15 16:01:05.251'),
	('3c62fbcaf90a453fac765fca89c4782e','yoaf','监管账户支付指令查询','2','','supAcctPayInst/index','xzp/supAcctPayInst/index','c1486f2e3551431dba1dc1e94068fd7d',10,'clipboard','1','admin','2025-07-17 16:26:05.984','admin','2025-07-17 16:26:05.984');

-- 功能权限数据插入
INSERT INTO bc_pmctl_function (id,menu_id,func_name,func_btn,func_uri,display_order,uri_type,func_desc,create_user,create_time,update_user,update_time) VALUES
	('c2f4ebf249c74b769e70f751db68f779','e6af76480a6246069ebf10060516cc0d','新中平 / 对账/清算不一致编辑','admin:recSettMismatch:edit',':POST',0,'1','新中平 / 对账/清算不一致编辑','admin','2025-07-11 10:57:16.823','admin','2025-07-11 10:57:16.823'),
	('50dbddc2211d499d8fa72f9fae71e0ef', '7e7f45dae6ef463686fe95be2225f850', '删除监管账户信息', 'admin:xzp:acctmgmt:remove', 'POST:/api/admin/xzp/deleteAcctInfo', 2, '1', '删除监管账户信息', 'admin', '2025-07-16 16:05:17.438', 'admin', '2025-07-16 16:05:17.438'),
	('2b98f48e38e048bf96d64df8e2520b21', '7e7f45dae6ef463686fe95be2225f850', '修改监管账户信息', 'admin:xzp:acctmgmt:edit', 'POST:/api/admin/xzp/updateAcctInfo', 1, '1', '修改监管账户', 'admin', '2025-07-16 16:04:23.158', 'admin', '2025-07-16 16:05:42.988'),
	('f1f16049cd0a4f8fabe35478b4c7173d','e6af76480a6246069ebf10060516cc0d','新中平 / 对账/清算不一致导出','admin:recSettMismatch:export',':GET',0,'1','新中平 / 对账/清算不一致导出','admin','2025-07-11 11:01:37.586','admin','2025-07-11 11:01:37.586');


-- 数据字典类型数据插入
INSERT INTO bc_pmctl_dict_type (dict_type_id,dict_type_name,data_source,status,dict_type_desc,create_user,create_time,update_user,update_time) VALUES
	('xzp_wl_url', '外联访问地址', '外联访问地址', '1', '外联访问地址', 'admin', '2025-07-09 16:43:36.592', 'admin', '2025-07-09 16:43:52.699'),
	('xzp_record_type','不符类型','tb_3502_xmykt_dz_result','1','不符类型','admin','2025-07-10 10:50:02.129','admin','2025-07-10 10:50:30.639'),
	('xzp_record_sta','对账结果','tb_3502_xmykt_dz_result','1','对账结果','admin','2025-07-10 17:17:55.488','admin','2025-07-10 17:17:55.488'),
	('xzp_tran_fg','交易状态/支付指令状态','tb_zjjg_tran_info','1','交易状态/支付指令状态','admin','2025-07-18 17:26:50.417','admin','2025-07-18 17:26:50.417'),
	('xzp_vch_type','支付类型','tb_zjjg_tran_info','1','支付类型','admin','2025-07-22 11:53:03.089','admin','2025-07-22 11:53:03.089');

-- 数据字典条目数据插入
INSERT INTO bc_pmctl_dict_entry (dict_type_id,dict_id,dict_name,display_order,status,create_user,create_time,update_user,update_time) VALUES
	('xzp_wl_url', 'w0000', 'http://127.0.0.1:9090/api/admin/xzp/queryYxjfLsxdye', 4, '1', 'admin', '2025-07-09 16:53:38.239', 'admin', '2025-07-11 18:18:24.976'),
	('xzp_record_type','1','1-对账不符',1,'1','admin','2025-07-10 10:51:56.898','admin','2025-07-10 10:51:56.898'),
	('xzp_record_type','2','2-签约对账不符',2,'2','admin','2025-07-10 10:52:31.4','admin','2025-07-10 10:52:31.4'),
	('xzp_record_type','3','3-清算日不符',3,'1','admin','2025-07-10 10:52:48.766','admin','2025-07-10 10:52:48.766'),
	('xzp_record_sta','1','1-商家成功，邮储不成功',1,'1','admin','2025-07-10 17:18:35.026','admin','2025-07-10 17:18:35.026'),
	('xzp_record_sta','2','2-邮储成功，商家不成功',2,'1','admin','2025-07-10 17:18:48.071','admin','2025-07-10 17:18:48.071'),
	('xzp_record_sta','3','3-无不符记录',3,'1','admin','2025-07-10 17:19:02.602','admin','2025-07-10 17:19:02.602'),
	('xzp_record_sta','4','4-双方不一致',4,'1','admin','2025-07-10 17:19:14.298','admin','2025-07-10 17:19:14.298'),
	('xzp_record_sta','5','5-委托单位成功,中平无流水',5,'1','admin','2025-07-10 17:19:39.337','admin','2025-07-10 17:19:39.337'),
	('xzp_record_sta','6','6-商家成功未冲正，邮储已冲正',6,'1','admin','2025-07-10 17:19:56.739','admin','2025-07-10 17:19:56.739'),
	('xzp_record_sta','7','7-清算日期不符',7,'1','admin','2025-07-10 17:20:11.268','admin','2025-07-10 17:20:11.268'),
	('xzp_record_sta','8','8-超时异常情况',8,'1','admin','2025-07-10 17:20:27.479','admin','2025-07-10 17:20:27.479'),
	('xzp_tran_fg','1','已支付',2,'1','admin','2025-07-18 17:27:31.799','admin','2025-07-18 17:27:31.799'),
	('xzp_tran_fg','0','收到指令，待支付',1,'1','admin','2025-07-18 17:27:20.681','admin','2025-07-22 11:50:22.235'),
	('xzp_tran_fg','2','未支付',3,'1','admin','2025-07-18 17:27:44.126','admin','2025-07-22 11:50:34.695'),
	('xzp_tran_fg','3','退款',4,'1','admin','2025-07-22 11:50:52.108','admin','2025-07-22 11:51:10.588'),
	('xzp_vch_type','1','同行',1,'1','admin','2025-07-22 11:53:21.743','admin','2025-07-22 11:53:21.743'),
	('xzp_vch_type','2','跨行',2,'1','admin','2025-07-22 11:53:32.08','admin','2025-07-22 11:53:32.08');
